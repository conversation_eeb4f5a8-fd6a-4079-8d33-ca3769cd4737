# Professional Arabic Portfolio Website

A stunning, high-end Arabic portfolio website built with modern web technologies, featuring GSAP animations, RTL support, and responsive design.

## 🌟 Features

### Design & Layout
- **RTL Support**: Full right-to-left layout for Arabic content
- **Dark Theme**: Elegant dark theme (#0F0F0F) with teal (#00BFA6) and orange (#FF6B00) accents
- **Typography**: IBM Plex Sans Arabic font from Google Fonts
- **Responsive**: Mobile-first design that works on all devices
- **Modern UI/UX**: Clean, professional design with generous spacing

### Animations & Interactions
- **GSAP Animations**: Smooth, professional animations throughout
- **ScrollTrigger**: Elements animate as they come into view
- **Animated Cursor**: Custom cursor with hover effects
- **Scroll Progress**: Visual progress indicator
- **Smooth Scrolling**: Enhanced navigation experience
- **Hover Effects**: Interactive elements with smooth transitions

### Sections
1. **Header**: Sticky navigation with mobile menu
2. **Hero**: Animated title, description, and profile image
3. **About**: Personal info with animated counters
4. **Services**: 3D flip cards with service details
5. **Skills**: Animated progress bars
6. **Projects**: Filterable gallery with modal details
7. **Testimonials**: Swiper carousel with client reviews
8. **Contact**: Form with floating labels
9. **Footer**: Social links and copyright

## 🚀 Technologies Used

- **HTML5**: Semantic markup
- **CSS3**: Modern styling with CSS Grid and Flexbox
- **JavaScript**: Interactive functionality
- **GSAP**: Professional animations and ScrollTrigger
- **Swiper.js**: Touch-enabled carousel
- **MixItUp**: Project filtering
- **Remix Icons**: Beautiful icon set

## 📁 Project Structure

```
├── index.html              # Main HTML file
├── assets/
│   ├── css/
│   │   └── style.css       # Main stylesheet
│   ├── js/
│   │   └── main.js         # JavaScript functionality
│   └── images/
│       ├── placeholder.svg # Placeholder image
│       ├── projects/       # Project images
│       ├── testimonials/   # Client photos
│       └── about/          # Profile images
└── README.md
```

## 🎨 Color Scheme

- **Primary**: #00BFA6 (Teal)
- **Secondary**: #00CFFF (Electric Blue)
- **Accent**: #FF6B00 (Orange)
- **Background**: #0F0F0F (Dark)
- **Container**: #1A1A1A (Dark Gray)
- **Text**: #FFFFFF (White) / #B3B3B3 (Light Gray)

## 📱 Responsive Breakpoints

- **Mobile**: < 568px
- **Tablet**: 568px - 768px
- **Desktop**: > 768px
- **Large Desktop**: > 1024px

## 🔧 Setup & Installation

1. Clone or download the project
2. Open `index.html` in a web browser
3. For development, use a local server:
   ```bash
   python3 -m http.server 3000
   ```
4. Visit `http://localhost:3000`

## ✨ Key Features Implemented

### GSAP Animations
- Hero text reveal with staggered animation
- Scroll-triggered section animations
- Animated counters and progress bars
- Modal animations with scale effects
- Cursor follower with blend modes

### RTL Support
- Proper Arabic text direction
- Mirrored layouts for RTL reading
- Arabic font optimization
- Right-aligned navigation

### Interactive Elements
- Project filtering system
- Modal popups for project details
- Testimonial carousel
- Contact form with validation
- Smooth scroll navigation

## 🎯 Performance Optimizations

- Optimized CSS with efficient selectors
- Lazy loading for images
- Minified external libraries
- Smooth 60fps animations
- Mobile-optimized touch interactions

## 🌐 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## 📝 Customization

### Colors
Update CSS variables in `:root` to change the color scheme:

```css
:root {
  --first-color: #00BFA6;
  --first-color-second: #00CFFF;
  --first-color-alt: #FF6B00;
  --body-color: #0F0F0F;
}
```

### Content
- Update text content in `index.html`
- Replace placeholder images in `assets/images/`
- Modify project data and testimonials
- Update contact information

### Animations
Customize GSAP animations in `assets/js/main.js`:

```javascript
gsap.from('.element', {
  y: 50,
  opacity: 0,
  duration: 0.8,
  ease: "power2.out"
});
```

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

---

**Built with ❤️ for the Arabic web development community**
