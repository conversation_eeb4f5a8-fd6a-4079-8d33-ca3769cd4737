/*=============== GSAP REGISTRATION ===============*/
gsap.registerPlugin(ScrollTrigger);

/*=============== SHOW MENU ===============*/
const navMenu = document.getElementById('nav-menu'),
      navToggle = document.getElementById('nav-toggle'),
      navClose = document.getElementById('nav-close');

/*===== MENU SHOW =====*/
if(navToggle){
    navToggle.addEventListener('click', () =>{
        navMenu.classList.add('show-menu');
        
        // GSAP animation for mobile menu
        gsap.fromTo('.nav__list', 
            { x: 300, opacity: 0 },
            { x: 0, opacity: 1, duration: 0.5, ease: "power2.out" }
        );
    });
}

/*===== MENU HIDDEN =====*/
if(navClose){
    navClose.addEventListener('click', () =>{
        navMenu.classList.remove('show-menu');
    });
}

/*=============== REMOVE MENU MOBILE ===============*/
const navLink = document.querySelectorAll('.nav__link');

function linkAction(){
    const navMenu = document.getElementById('nav-menu');
    navMenu.classList.remove('show-menu');
}
navLink.forEach(n => n.addEventListener('click', linkAction));

/*=============== CHANGE BACKGROUND HEADER ===============*/
function scrollHeader(){
    const nav = document.getElementById('header');
    if(this.scrollY >= 80) nav.classList.add('scroll-header'); 
    else nav.classList.remove('scroll-header');
}
window.addEventListener('scroll', scrollHeader);

/*=============== HERO ANIMATIONS ===============*/
// Hero text reveal animation
gsap.timeline()
    .from('.hero__title-line', {
        y: 100,
        opacity: 0,
        duration: 1,
        stagger: 0.2,
        ease: "power2.out"
    })
    .from('.hero__description', {
        y: 50,
        opacity: 0,
        duration: 0.8,
        ease: "power2.out"
    }, "-=0.5")
    .from('.hero__cta', {
        y: 30,
        opacity: 0,
        duration: 0.6,
        ease: "power2.out"
    }, "-=0.3")
    .from('.hero__blob', {
        scale: 0,
        opacity: 0,
        duration: 1,
        ease: "back.out(1.7)"
    }, "-=0.8")
    .from('.hero__scroll', {
        y: 30,
        opacity: 0,
        duration: 0.6,
        ease: "power2.out"
    }, "-=0.4");

/*=============== SCROLL ANIMATIONS ===============*/
// About section animations
ScrollTrigger.create({
    trigger: ".about",
    start: "top 80%",
    onEnter: () => {
        gsap.timeline()
            .from('.about__description p', {
                y: 50,
                opacity: 0,
                duration: 0.8,
                stagger: 0.2,
                ease: "power2.out"
            })
            .from('.about__stat', {
                y: 30,
                opacity: 0,
                duration: 0.6,
                stagger: 0.1,
                ease: "power2.out"
            }, "-=0.4")
            .from('.about__img', {
                scale: 0.8,
                opacity: 0,
                duration: 1,
                ease: "power2.out"
            }, "-=0.6");
    }
});

// Services cards animation
ScrollTrigger.create({
    trigger: ".services",
    start: "top 80%",
    onEnter: () => {
        gsap.from('.services__card', {
            y: 100,
            opacity: 0,
            duration: 0.8,
            stagger: 0.2,
            ease: "power2.out"
        });
    }
});

// Skills bars animation
ScrollTrigger.create({
    trigger: ".skills",
    start: "top 80%",
    onEnter: () => {
        gsap.timeline()
            .from('.skills__data', {
                x: -50,
                opacity: 0,
                duration: 0.6,
                stagger: 0.1,
                ease: "power2.out"
            })
            .from('.skills__percentage', {
                width: 0,
                duration: 1.5,
                stagger: 0.1,
                ease: "power2.out"
            }, "-=0.3");
    }
});

// Projects animation
ScrollTrigger.create({
    trigger: ".projects",
    start: "top 80%",
    onEnter: () => {
        gsap.from('.projects__card', {
            y: 80,
            opacity: 0,
            duration: 0.8,
            stagger: 0.15,
            ease: "power2.out"
        });
    }
});

// Contact form animation
ScrollTrigger.create({
    trigger: ".contact",
    start: "top 80%",
    onEnter: () => {
        gsap.timeline()
            .from('.contact__card', {
                y: 50,
                opacity: 0,
                duration: 0.6,
                stagger: 0.1,
                ease: "power2.out"
            })
            .from('.contact__form-div', {
                y: 30,
                opacity: 0,
                duration: 0.5,
                stagger: 0.1,
                ease: "power2.out"
            }, "-=0.3");
    }
});

/*=============== ANIMATED COUNTERS ===============*/
function animateCounters() {
    const counters = document.querySelectorAll('.about__stat-number');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2;
        
        gsap.to(counter, {
            innerHTML: target,
            duration: duration,
            ease: "power2.out",
            snap: { innerHTML: 1 },
            onUpdate: function() {
                counter.innerHTML = Math.ceil(counter.innerHTML);
            }
        });
    });
}

// Trigger counters animation when about section is visible
ScrollTrigger.create({
    trigger: ".about__stats",
    start: "top 80%",
    onEnter: animateCounters
});

/*=============== PROJECTS FILTER ===============*/
let mixerProjects = mixitup('.projects__content', {
    selectors: {
        target: '.projects__card'
    },
    animation: {
        duration: 400
    }
});

/* Link active work */ 
const linkWork = document.querySelectorAll('.projects__item');

function activeWork(){
    linkWork.forEach(l=> l.classList.remove('active-work'));
    this.classList.add('active-work');
}

linkWork.forEach(l=> l.addEventListener('click', activeWork));

/*=============== TESTIMONIALS SWIPER ===============*/
let swiperTestimonials = new Swiper(".testimonials__content", {
    loop: true,
    grabCursor: true,
    spaceBetween: 48,

    pagination: {
        el: ".swiper-pagination",
        clickable: true,
        dynamicBullets: true,
    },

    navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
    },

    breakpoints:{
        568:{
            slidesPerView: 2,
        }
    }
});

/*=============== SCROLL SECTIONS ACTIVE LINK ===============*/
const sections = document.querySelectorAll('section[id]');

function scrollActive(){
    const scrollY = window.pageYOffset;

    sections.forEach(current =>{
        const sectionHeight = current.offsetHeight;
        const sectionTop = current.offsetTop - 50;
        const sectionId = current.getAttribute('id');

        if(scrollY > sectionTop && scrollY <= sectionTop + sectionHeight){
            document.querySelector('.nav__menu a[href*=' + sectionId + ']').classList.add('active-link');
        }else{
            document.querySelector('.nav__menu a[href*=' + sectionId + ']').classList.remove('active-link');
        }
    });
}
window.addEventListener('scroll', scrollActive);

/*=============== SHOW SCROLL UP ===============*/ 
function scrollUp(){
    const scrollUp = document.getElementById('scroll-up');
    if(this.scrollY >= 560) scrollUp.classList.add('show-scroll'); 
    else scrollUp.classList.remove('show-scroll');
}
window.addEventListener('scroll', scrollUp);

/*=============== PROJECT MODAL ===============*/
const projectModal = document.getElementById('project-modal'),
      projectButtons = document.querySelectorAll('.projects__button'),
      modalClose = document.getElementById('modal-close');

// Open modal
projectButtons.forEach(button => {
    button.addEventListener('click', (e) => {
        e.preventDefault();
        projectModal.classList.add('active-modal');
        
        // GSAP modal animation
        gsap.fromTo('.project__modal-content', 
            { scale: 0.5, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.3, ease: "back.out(1.7)" }
        );
    });
});

// Close modal
modalClose.addEventListener('click', () => {
    projectModal.classList.remove('active-modal');
});

// Close modal when clicking outside
projectModal.addEventListener('click', (e) => {
    if (e.target === projectModal) {
        projectModal.classList.remove('active-modal');
    }
});

/*=============== ANIMATED CURSOR ===============*/
const cursor = document.createElement('div');
cursor.className = 'cursor';
document.body.appendChild(cursor);

// Add cursor styles
const cursorStyle = document.createElement('style');
cursorStyle.textContent = `
    .cursor {
        width: 20px;
        height: 20px;
        background: linear-gradient(45deg, var(--first-color), var(--first-color-second));
        border-radius: 50%;
        position: fixed;
        pointer-events: none;
        z-index: 9999;
        mix-blend-mode: difference;
        transition: transform 0.1s ease;
    }
    
    .cursor.hover {
        transform: scale(2);
    }
`;
document.head.appendChild(cursorStyle);

// Cursor movement
document.addEventListener('mousemove', (e) => {
    gsap.to(cursor, {
        x: e.clientX - 10,
        y: e.clientY - 10,
        duration: 0.1,
        ease: "power2.out"
    });
});

// Cursor hover effects
const hoverElements = document.querySelectorAll('a, button, .projects__card, .services__card');
hoverElements.forEach(el => {
    el.addEventListener('mouseenter', () => cursor.classList.add('hover'));
    el.addEventListener('mouseleave', () => cursor.classList.remove('hover'));
});

/*=============== SCROLL PROGRESS INDICATOR ===============*/
const progressBar = document.createElement('div');
progressBar.className = 'scroll-progress';
document.body.appendChild(progressBar);

// Add progress bar styles
const progressStyle = document.createElement('style');
progressStyle.textContent = `
    .scroll-progress {
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: linear-gradient(90deg, var(--first-color), var(--first-color-second));
        z-index: 9999;
        transition: width 0.1s ease;
    }
`;
document.head.appendChild(progressStyle);

// Update progress on scroll
window.addEventListener('scroll', () => {
    const scrollTop = document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    const scrollPercent = (scrollTop / scrollHeight) * 100;
    
    gsap.to(progressBar, {
        width: scrollPercent + '%',
        duration: 0.1,
        ease: "none"
    });
});

/*=============== SMOOTH SCROLLING ===============*/
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            gsap.to(window, {
                duration: 1,
                scrollTo: target,
                ease: "power2.inOut"
            });
        }
    });
});
