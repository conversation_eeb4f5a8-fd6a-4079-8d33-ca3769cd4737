/*=============== GOOGLE FONTS ===============*/
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/*=============== VARIABLES CSS ===============*/
:root {
  --header-height: 3.5rem;

  /*========== Colors ==========*/
  --hue: 162;
  --first-color: #00BFA6;
  --first-color-second: #00CFFF;
  --first-color-alt: #FF6B00;
  --first-color-lighter: hsl(var(--hue), 92%, 85%);
  --title-color: #FFFFFF;
  --text-color: #B3B3B3;
  --text-color-light: #808080;
  --input-color: #1A1A1A;
  --body-color: #0F0F0F;
  --container-color: #1A1A1A;
  --scroll-bar-color: #2A2A2A;
  --scroll-thumb-color: #3A3A3A;

  /*========== Font and typography ==========*/
  --body-font: 'IBM Plex Sans Arabic', sans-serif;

  /* .5rem = 8px, 1rem = 16px, 1.5rem = 24px ... */
  --big-font-size: 3rem;
  --h1-font-size: 2.25rem;
  --h2-font-size: 1.5rem;
  --h3-font-size: 1.25rem;
  --normal-font-size: 1rem;
  --small-font-size: .875rem;
  --smaller-font-size: .813rem;

  /*========== Font weight ==========*/
  --font-light: 300;
  --font-medium: 500;
  --font-semi-bold: 600;
  --font-bold: 700;

  /*========== Margins Bottom ==========*/
  --mb-0-25: .25rem;
  --mb-0-5: .5rem;
  --mb-0-75: .75rem;
  --mb-1: 1rem;
  --mb-1-5: 1.5rem;
  --mb-2: 2rem;
  --mb-2-5: 2.5rem;
  --mb-3: 3rem;

  /*========== z index ==========*/
  --z-tooltip: 10;
  --z-fixed: 100;
  --z-modal: 1000;
}

/* Font size for large devices */
@media screen and (min-width: 968px) {
  :root {
    --big-font-size: 4rem;
    --h1-font-size: 2.75rem;
    --h2-font-size: 1.75rem;
    --h3-font-size: 1.5rem;
    --normal-font-size: 1.125rem;
    --small-font-size: 1rem;
    --smaller-font-size: .875rem;
  }
}

/*=============== BASE ===============*/
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
  direction: rtl;
}

body {
  margin: 0 0 var(--header-height) 0;
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
  background-color: var(--body-color);
  color: var(--text-color);
  line-height: 1.8;
}

h1, h2, h3, h4 {
  color: var(--title-color);
  font-weight: var(--font-semi-bold);
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
}

/*=============== REUSABLE CSS CLASSES ===============*/
.section {
  padding: 4rem 0 2rem;
}

@media screen and (min-width: 768px) {
  .section {
    padding: 6rem 0 2rem;
  }
}

.section__title {
  font-size: var(--h1-font-size);
  color: var(--title-color);
  text-align: center;
  margin-bottom: var(--mb-0-25);
}

.section__subtitle {
  display: block;
  font-size: var(--small-font-size);
  color: var(--first-color);
  text-align: center;
  margin-bottom: var(--mb-3);
  font-weight: var(--font-medium);
}

/*=============== LAYOUT ===============*/
.container {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

@media screen and (min-width: 1200px) {
  .container {
    padding-left: 0;
    padding-right: 0;
  }
}

.grid {
  display: grid;
}

/*=============== BUTTONS ===============*/
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--first-color);
  color: #FFF;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-weight: var(--font-medium);
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
}

.btn:hover {
  background-color: var(--first-color-alt);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 191, 166, 0.3);
}

.btn--outline {
  background-color: transparent;
  border: 2px solid var(--first-color);
  color: var(--first-color);
}

.btn--outline:hover {
  background-color: var(--first-color);
  color: #FFF;
}

/*=============== HEADER & NAV ===============*/
.header {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: var(--z-fixed);
  background-color: var(--body-color);
  transition: all 0.3s ease;
}

.nav {
  max-width: 1200px;
  height: var(--header-height);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav__logo,
.nav__toggle {
  color: var(--title-color);
  font-weight: var(--font-medium);
}

.nav__logo:hover {
  color: var(--first-color);
}

.nav__toggle {
  font-size: 1.1rem;
  cursor: pointer;
}

.nav__toggle:hover {
  color: var(--first-color);
}

@media screen and (max-width: 767px) {
  .nav__menu {
    position: fixed;
    bottom: -100%;
    left: 0;
    width: 100%;
    background-color: var(--body-color);
    padding: 2rem 1.5rem 4rem;
    box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.15);
    border-radius: 1.5rem 1.5rem 0 0;
    transition: 0.3s;
  }
}

.nav__list {
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.nav__link {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: var(--small-font-size);
  color: var(--title-color);
  font-weight: var(--font-medium);
  transition: 0.3s;
}

.nav__link:hover {
  color: var(--first-color);
}

.nav__icon {
  font-size: 1.2rem;
}

.nav__close {
  position: absolute;
  right: 1.3rem;
  bottom: 0.5rem;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--first-color);
}

.nav__close:hover {
  color: var(--first-color-alt);
}

/* show menu */
.show-menu {
  bottom: 0;
}

/* Active link */
.active-link {
  color: var(--first-color);
}

/* Change background header */
.scroll-header {
  bottom: initial;
  top: 0;
  background-color: rgba(15, 15, 15, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/*=============== HERO ===============*/
.hero {
  background: linear-gradient(135deg, var(--body-color) 0%, #1A1A1A 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero__container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: center;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.hero__content {
  order: 1;
}

.hero__image {
  order: 2;
  justify-self: center;
}

.hero__title {
  font-size: var(--big-font-size);
  margin-bottom: var(--mb-0-75);
  line-height: 1.2;
  font-weight: var(--font-bold);
}

.hero__title-line {
  display: block;
}

.hero__title-accent {
  background: linear-gradient(45deg, var(--first-color), var(--first-color-second));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero__description {
  margin-bottom: var(--mb-2);
  font-size: var(--h3-font-size);
  color: var(--text-color);
  line-height: 1.6;
}

.hero__cta {
  font-size: var(--h3-font-size);
  padding: 1.25rem 2.5rem;
}

.hero__blob {
  width: 200px;
  fill: var(--first-color);
}

.hero__blob-img {
  width: 170px;
  height: 170px;
  object-fit: cover;
  border-radius: 50%;
}

.hero__scroll {
  position: absolute;
  right: 1.5rem;
  bottom: 5rem;
  display: grid;
  row-gap: 1rem;
  justify-items: center;
  color: var(--first-color);
}

.hero__scroll-button {
  color: var(--first-color);
  transition: 0.3s;
}

.hero__scroll-button:hover {
  transform: translateY(0.25rem);
}

.hero__scroll-mouse {
  font-size: 2rem;
}

.hero__scroll-name {
  font-size: var(--small-font-size);
  color: var(--title-color);
  font-weight: var(--font-medium);
  margin-right: var(--mb-0-25);
  transform: rotate(-90deg);
}

/*=============== ABOUT ===============*/
.about__container {
  gap: 3rem;
}

.about__content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: center;
}

.about__info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.about__description {
  text-align: justify;
  margin-bottom: var(--mb-2);
}

.about__description p {
  margin-bottom: var(--mb-1);
}

.about__stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: var(--mb-2);
}

.about__stat {
  text-align: center;
  padding: 1.5rem;
  background-color: var(--container-color);
  border-radius: 0.5rem;
  transition: 0.3s;
}

.about__stat:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 191, 166, 0.15);
}

.about__stat-number {
  display: block;
  font-size: var(--h1-font-size);
  font-weight: var(--font-bold);
  color: var(--first-color);
  margin-bottom: var(--mb-0-25);
}

.about__stat-text {
  font-size: var(--small-font-size);
  color: var(--text-color);
}

.about__image {
  justify-self: center;
}

.about__img {
  width: 200px;
  border-radius: 1rem;
  justify-self: center;
  transition: 0.3s;
}

.about__img:hover {
  transform: scale(1.05);
}

/*=============== SERVICES ===============*/
.services__container {
  gap: 1.5rem;
}

.services__content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.services__card {
  perspective: 1000px;
  height: 300px;
}

.services__card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.services__card:hover .services__card-inner {
  transform: rotateY(180deg);
}

.services__card-front,
.services__card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  background-color: var(--container-color);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: 0.3s;
}

.services__card-back {
  transform: rotateY(180deg);
  background: linear-gradient(135deg, var(--first-color), var(--first-color-second));
}

.services__icon {
  font-size: 3rem;
  color: var(--first-color);
  margin-bottom: var(--mb-1);
}

.services__title {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-1);
  color: var(--title-color);
}

.services__description {
  font-size: var(--normal-font-size);
  color: var(--text-color);
  line-height: 1.6;
}

.services__card-back .services__title {
  color: #FFF;
  margin-bottom: var(--mb-1-5);
}

.services__list {
  list-style: none;
  color: #FFF;
}

.services__list li {
  margin-bottom: var(--mb-0-5);
  position: relative;
  padding-right: 1rem;
}

.services__list li::before {
  content: "✓";
  position: absolute;
  right: 0;
  color: #FFF;
  font-weight: var(--font-bold);
}

/*=============== SKILLS ===============*/
.skills__container {
  gap: 0;
}

.skills__content {
  display: grid;
  gap: 1.5rem;
}

.skills__data {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skills__info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skills__name {
  font-size: var(--normal-font-size);
  font-weight: var(--font-medium);
  color: var(--title-color);
}

.skills__number {
  font-weight: var(--font-medium);
  color: var(--first-color);
}

.skills__bar {
  height: 8px;
  background-color: var(--container-color);
  border-radius: 4px;
  overflow: hidden;
}

.skills__percentage {
  display: block;
  height: 100%;
  background: linear-gradient(90deg, var(--first-color), var(--first-color-second));
  border-radius: 4px;
  transition: width 2s ease;
}

.skills__html {
  width: 90%;
}

.skills__css {
  width: 85%;
}

.skills__js {
  width: 80%;
}

.skills__react {
  width: 85%;
}

.skills__vue {
  width: 75%;
}

.skills__node {
  width: 70%;
}

/*=============== PROJECTS ===============*/
.projects__filters {
  display: flex;
  justify-content: center;
  align-items: center;
  column-gap: 0.75rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.projects__item {
  cursor: pointer;
  color: var(--title-color);
  padding: 0.25rem 0.75rem;
  font-weight: var(--font-medium);
  border-radius: 0.5rem;
  transition: 0.3s;
}

.projects__item:hover {
  background-color: var(--first-color);
  color: #FFF;
}

.active-work {
  background-color: var(--first-color);
  color: #FFF;
}

.projects__content {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.25rem;
  justify-content: center;
}

.projects__card {
  position: relative;
  background-color: var(--container-color);
  padding: 1rem;
  border-radius: 1rem;
  overflow: hidden;
  transition: 0.3s;
}

.projects__card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 32px rgba(0, 191, 166, 0.15);
}

.projects__img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 1rem;
  margin-bottom: var(--mb-1);
}

.projects__modal {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: -100%;
  left: 0;
  background: linear-gradient(180deg,
              hsla(var(--hue), 24%, 40%, 0.3) 0%,
              hsla(var(--hue), 24%, 4%, 1) 95%);
  display: grid;
  align-items: flex-end;
  padding: 1.5rem 1.25rem;
  transition: 0.3s;
}

.projects__subtitle,
.projects__title {
  color: #FFF;
}

.projects__subtitle {
  font-size: var(--smaller-font-size);
}

.projects__title {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-0-5);
}

.projects__button {
  padding: 0.5rem;
  color: #FFF;
  font-size: var(--small-font-size);
  display: flex;
  align-items: center;
  column-gap: 0.25rem;
  cursor: pointer;
}

.projects__button:hover .projects__button-icon {
  transform: translateX(-0.25rem);
}

.projects__card:hover .projects__modal {
  bottom: 0;
}

/*=============== TESTIMONIALS ===============*/
.testimonials__container {
  gap: 0;
}

.testimonials__card {
  background-color: var(--container-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem 2rem;
  border-radius: 1.5rem;
  margin-bottom: 3rem;
  position: relative;
}

.testimonials__quote {
  display: inline-flex;
  background-color: var(--first-color);
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  color: #FFF;
  font-size: 1.5rem;
  margin-bottom: var(--mb-1);
}

.testimonials__description {
  margin-bottom: var(--mb-1);
  color: var(--text-color);
  line-height: 1.6;
}

.testimonials__date {
  font-size: var(--normal-font-size);
  margin-bottom: var(--mb-1);
  color: var(--text-color-light);
}

.testimonials__perfil {
  display: flex;
  align-items: center;
  column-gap: 1rem;
}

.testimonials__perfil-img {
  width: 60px;
  height: 60px;
  border-radius: 3rem;
  object-fit: cover;
}

.testimonials__perfil-name {
  font-size: var(--h3-font-size);
  font-weight: var(--font-medium);
  color: var(--title-color);
}

.testimonials__perfil-detail {
  font-size: var(--small-font-size);
  color: var(--text-color-light);
}

/* Swiper class */
.swiper-button-prev::after,
.swiper-button-next::after {
  content: '';
}

.swiper-button-next,
.swiper-button-prev {
  height: initial;
  width: initial;
  margin: initial;
  font-size: 2.5rem;
  color: var(--first-color);
}

.swiper-button-next {
  right: -0.5rem;
  margin-right: 0;
}

.swiper-button-prev {
  left: -0.5rem;
  margin-left: 0;
}

.swiper-horizontal > .swiper-pagination-bullets {
  bottom: -2.5rem;
}

.swiper-pagination-bullet-active {
  background-color: var(--first-color);
}

.swiper-button-next,
.swiper-button-prev,
.swiper-pagination-bullet {
  outline: none;
}

/*=============== CONTACT ===============*/
.contact__container {
  gap: 3rem;
}

.contact__content {
  display: grid;
  gap: 2rem;
}

.contact__info {
  display: grid;
  gap: 1rem;
}

.contact__card {
  background-color: var(--container-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  text-align: center;
  transition: 0.3s;
}

.contact__card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 191, 166, 0.15);
}

.contact__card-icon {
  font-size: 2rem;
  color: var(--first-color);
  margin-bottom: var(--mb-0-25);
}

.contact__card-title,
.contact__card-data {
  font-size: var(--small-font-size);
}

.contact__card-title {
  color: var(--title-color);
  font-weight: var(--font-medium);
}

.contact__card-data {
  display: block;
  margin-top: var(--mb-0-75);
  color: var(--text-color);
}

.contact__form {
  display: grid;
  gap: 1.25rem;
}

.contact__form-div {
  position: relative;
  height: 4rem;
}

.contact__form-area {
  height: 7rem;
}

.contact__form-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid var(--text-color-light);
  background: none;
  color: var(--text-color);
  outline: none;
  border-radius: 0.75rem;
  padding: 1.5rem;
  z-index: 1;
  font-family: var(--body-font);
  resize: none;
  transition: 0.3s;
}

.contact__form-input:focus {
  border-color: var(--first-color);
}

.contact__form-tag {
  position: absolute;
  top: -0.75rem;
  right: 1.25rem;
  font-size: var(--smaller-font-size);
  padding: 0.25rem;
  background-color: var(--body-color);
  color: var(--first-color);
  z-index: 10;
}

.contact__button {
  justify-self: center;
  cursor: pointer;
}

/*=============== FOOTER ===============*/
.footer {
  padding-top: 2rem;
}

.footer__bg {
  background-color: var(--container-color);
  padding: 2rem 0 3rem;
}

.footer__container {
  gap: 3.5rem;
}

.footer__content {
  display: grid;
  gap: 2rem;
  text-align: center;
}

.footer__title {
  font-size: var(--h1-font-size);
  margin-bottom: var(--mb-0-25);
  color: var(--title-color);
}

.footer__subtitle {
  color: var(--text-color-light);
}

.footer__links {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.footer__link:hover {
  color: var(--first-color);
}

.footer__socials {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.footer__social {
  background-color: var(--first-color);
  color: #FFF;
  font-size: 1.125rem;
  padding: 0.4rem;
  border-radius: 0.5rem;
  display: inline-flex;
  transition: 0.3s;
}

.footer__social:hover {
  background-color: var(--first-color-alt);
  transform: translateY(-2px);
}

.footer__copy {
  font-size: var(--smaller-font-size);
  text-align: center;
  color: var(--text-color-light);
  margin-top: 3rem;
}

/*=============== SCROLL UP ===============*/
.scrollup {
  position: fixed;
  right: 1rem;
  bottom: -20%;
  background-color: var(--first-color);
  opacity: 0.8;
  padding: 0 0.3rem;
  border-radius: 0.4rem;
  z-index: var(--z-tooltip);
  transition: 0.4s;
}

.scrollup:hover {
  background-color: var(--first-color-alt);
  opacity: 1;
}

.scrollup__icon {
  font-size: 1.5rem;
  color: #FFF;
}

/* Show scroll */
.show-scroll {
  bottom: 5rem;
}

/*=============== PROJECT MODAL ===============*/
.project__modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1rem;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
}

.project__modal.active-modal {
  opacity: 1;
  visibility: visible;
}

.project__modal-content {
  position: relative;
  background-color: var(--container-color);
  padding: 1.5rem;
  border-radius: 1.5rem;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.project__modal-close {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background-color: var(--first-color);
  color: #FFF;
  font-size: 1.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.4rem;
  cursor: pointer;
  transition: 0.3s;
}

.project__modal-close:hover {
  background-color: var(--first-color-alt);
}

.project__modal-title {
  font-size: var(--h2-font-size);
  color: var(--title-color);
  margin-bottom: var(--mb-1);
  text-align: center;
}

.project__modal-image {
  margin-bottom: var(--mb-1);
}

.project__modal-img {
  width: 100%;
  border-radius: 0.5rem;
}

.project__modal-description {
  color: var(--text-color);
  line-height: 1.6;
  margin-bottom: var(--mb-1-5);
}

.project__modal-tech h4 {
  color: var(--title-color);
  margin-bottom: var(--mb-0-75);
}

.project__modal-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: var(--mb-2);
}

.project__modal-tag {
  background-color: var(--first-color);
  color: #FFF;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: var(--smaller-font-size);
}

.project__modal-links {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/*=============== SCROLL BAR ===============*/
::-webkit-scrollbar {
  width: 0.60rem;
  background-color: var(--scroll-bar-color);
  border-radius: 0.5rem;
}

::-webkit-scrollbar-thumb {
  background-color: var(--scroll-thumb-color);
  border-radius: 0.5rem;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-color-light);
}

/*=============== MEDIA QUERIES ===============*/
/* Base styles are mobile-first */
.hero__container {
  grid-template-columns: 1fr;
  text-align: center;
}

.hero__content {
  order: 2;
}

.hero__image {
  order: 1;
  margin-bottom: 2rem;
}

.about__content {
  grid-template-columns: 1fr;
  text-align: center;
}

.services__content {
  grid-template-columns: 1fr;
}

.projects__content {
  grid-template-columns: 1fr;
}

.contact__content {
  grid-template-columns: 1fr;
}

.footer__content {
  grid-template-columns: 1fr;
}

/* For small devices */
@media screen and (max-width: 350px) {
  .container {
    margin-left: var(--mb-1);
    margin-right: var(--mb-1);
  }

  .nav__menu {
    padding: 2rem 0.25rem 4rem;
  }
  .nav__list {
    column-gap: 0;
  }

  .hero__container {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero__content {
    order: 2;
  }

  .hero__image {
    order: 1;
  }

  .hero__blob {
    width: 180px;
  }

  .about__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .services__content {
    grid-template-columns: 1fr;
  }

  .projects__content {
    grid-template-columns: 1fr;
  }

  .contact__content {
    grid-template-columns: 1fr;
  }
}

/* For medium devices */
@media screen and (min-width: 568px) {
  .hero__container {
    grid-template-columns: 1fr 1fr;
  }

  .hero__content {
    order: 1;
  }

  .hero__image {
    order: 2;
    justify-self: center;
  }

  .about__content {
    grid-template-columns: 2fr 1fr;
  }

  .services__content {
    grid-template-columns: repeat(2, 1fr);
  }

  .projects__content {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact__content {
    grid-template-columns: 1fr 2fr;
  }

  .footer__content {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media screen and (min-width: 768px) {
  .container {
    margin-left: auto;
    margin-right: auto;
  }

  body {
    margin: 0;
  }

  .section {
    padding: 6rem 0 2rem;
  }
  .section__subtitle {
    margin-bottom: 4rem;
  }

  .header {
    top: 0;
    bottom: initial;
    background-color: rgba(15, 15, 15, 0.95);
    backdrop-filter: blur(10px);
  }

  .header,
  .main,
  .footer__container {
    padding: 0 1rem;
  }

  .nav {
    height: calc(var(--header-height) + 1.5rem);
    column-gap: 1rem;
  }
  .nav__icon,
  .nav__close,
  .nav__toggle {
    display: none;
  }
  .nav__list {
    display: flex;
    column-gap: 2rem;
    grid-template-columns: none;
  }
  .nav__menu {
    margin-left: auto;
    position: static;
    background-color: transparent;
    box-shadow: none;
    border-radius: 0;
    padding: 0;
  }

  .hero__container {
    row-gap: 5rem;
  }
  .hero__content {
    padding-top: 5.5rem;
    column-gap: 2rem;
  }
  .hero__blob {
    width: 270px;
  }
  .hero__scroll {
    display: block;
  }
  .hero__scroll-button {
    margin-left: 3rem;
  }

  .about__container {
    column-gap: 5rem;
  }
  .about__img {
    width: 350px;
  }
  .about__description {
    text-align: initial;
  }
  .about__info {
    justify-content: space-between;
  }
  .about__buttons {
    justify-content: initial;
  }

  .qualification__tabs {
    justify-content: center;
  }
  .qualification__button {
    margin: 0 var(--mb-1);
  }
  .qualification__sections {
    grid-template-columns: 0.5fr;
  }

  .services__container {
    grid-template-columns: repeat(3, 218px);
    justify-content: center;
  }
  .services__icon {
    font-size: 2rem;
  }
  .services__content {
    padding: 6rem 0 2rem 2.5rem;
  }
  .services__modal-content {
    width: 450px;
  }

  .portfolio__img {
    width: 320px;
  }
  .portfolio__content {
    align-items: center;
  }

  .projects__content {
    grid-template-columns: repeat(3, 350px);
  }

  .footer__container {
    grid-template-columns: repeat(3, 1fr);
  }
  .footer__bg {
    padding: 3rem 0 3.5rem;
  }
  .footer__links {
    flex-direction: row;
    column-gap: 2rem;
  }
  .footer__socials {
    justify-self: flex-end;
  }
  .footer__copy {
    margin-top: 4.5rem;
  }
}

/* For large devices */
@media screen and (min-width: 1024px) {
  .header,
  .main,
  .footer__container {
    padding: 0;
  }

  .hero__blob {
    width: 320px;
  }
  .hero__social {
    transform: translateX(-6rem);
  }

  .services__container {
    grid-template-columns: repeat(3, 238px);
  }

  .portfolio__content,
  .projects__content {
    column-gap: 3rem;
  }

  .swiper-portfolio-icon {
    font-size: 3.5rem;
  }
  .swiper-button-prev {
    left: -3.5rem;
  }
  .swiper-button-next {
    right: -3.5rem;
  }
  .swiper-horizontal > .swiper-pagination-bullets {
    bottom: -4.5rem;
  }

  .contact__form {
    width: 460px;
  }
  .contact__inputs {
    grid-template-columns: repeat(2, 1fr);
  }
}
